# Wio Terminal Countdown Timer Project

## Project Overview

This project implements a countdown timer application for the Seeed Studio Wio Terminal, following the LEIA (LLM-driven Embedded Interface Architecture) declarative UI framework principles. The application provides three preset countdown timers (30s, 10s, 5s) controlled by the Wio Terminal's physical buttons.

## What We've Accomplished

### 1. Research and Analysis
- **LEIA Framework Study**: Analyzed the LEIA declarative UI framework structure, understanding its JSON-based approach to defining user interfaces for embedded systems
- **Wio Terminal Documentation**: Researched button usage patterns, display capabilities, and hardware specifications
- **Technical Requirements**: Mapped project requirements to technical implementation approach

### 2. Knowledge Base Development
Created comprehensive documentation in the `knowledge_base/` directory:
- **LEIA Framework Basics**: Core JSON structure, element types, layout system, and styling approach
- **Wio Terminal Buttons**: Button definitions, physical layout, configuration, and usage patterns

### 3. Project Structure
Established proper directory structure following the project guidelines:
```
├── projects/
│   ├── countdown_timer.ino          # Main Arduino code
│   ├── countdown_timer_ui.json      # LEIA-style UI definition
│   ├── USAGE_INSTRUCTIONS.md        # Detailed usage guide
│   └── tmp/
│       └── setup_arduino_cli.sh     # Arduino CLI setup script
├── knowledge_base/
│   ├── LEIA_Framework_Basics.md     # LEIA framework documentation
│   └── Wio_Terminal_Buttons.md      # Button usage documentation
├── Todo.md                          # Project progress tracking
└── README.md                        # This file
```

### 4. Code Implementation

#### JSON UI Definition (`countdown_timer_ui.json`)
- Declarative UI structure following LEIA principles
- Hierarchical layout with containers and elements
- Button hints, countdown display, and status indicators
- Color-coded visual elements for better user experience

#### Arduino Implementation (`countdown_timer.ino`)
- **Display Management**: TFT_eSPI library integration with 320x240 landscape display
- **Button Handling**: Proper INPUT_PULLUP configuration with 200ms debounce
- **Timer Logic**: Non-blocking countdown implementation using millis()
- **State Management**: Clean state machine (READY → COUNTING → FINISHED)
- **Visual Feedback**: Color-coded urgency indication and clear status display
- **Interrupt Capability**: Any button press during countdown stops the timer

### 5. Key Features Implemented

#### User Interface
- **Button Layout Hints**: Visual representation of physical buttons at screen top
- **Large Countdown Display**: Easy-to-read numbers with color coding
- **Status Indicators**: Clear feedback for current timer state
- **Responsive Design**: Optimized for 320x240 display resolution

#### Functionality
- **Three Timer Options**: 30s (Button C), 10s (Button B), 5s (Button A)
- **Visual Urgency**: Green → Orange → Red color progression as time decreases
- **Interrupt Control**: Press any button during countdown to stop
- **Auto Reset**: Automatic return to ready state after completion
- **Debounced Input**: Reliable button handling with 200ms debounce

### 6. Technical Approach

#### LEIA-Inspired Architecture
While the Wio Terminal doesn't have full JSON parsing capabilities for real-time UI generation, we implemented the LEIA principles by:
- Creating a JSON UI definition that describes the interface structure
- Implementing corresponding display functions that render the defined UI elements
- Maintaining separation between UI definition and rendering logic
- Using declarative approach for UI structure description

#### Libraries and Dependencies
- **TFT_eSPI**: Display management and graphics rendering
- **ArduinoJson**: JSON parsing capability (prepared for future enhancements)
- **Standard Arduino**: Core functionality and timing

## Next Steps

### Immediate Actions Needed
1. **Arduino CLI Setup**: Install Arduino CLI and required libraries
2. **Compilation**: Compile the code for Wio Terminal
3. **Testing**: Upload and test on physical hardware
4. **Validation**: Verify all timer functions and button responses

### Future Enhancements
1. **Full JSON Parsing**: Implement runtime JSON UI parsing for dynamic interfaces
2. **Sound Feedback**: Add buzzer alerts for timer completion
3. **Custom Timers**: Allow user-defined countdown durations
4. **Multiple Screens**: Implement screen navigation and settings

## How to Use

1. **Setup**: Follow instructions in `projects/USAGE_INSTRUCTIONS.md`
2. **Compilation**: Use the setup script in `projects/tmp/setup_arduino_cli.sh`
3. **Operation**: 
   - Press Button C (left) for 30-second timer
   - Press Button B (middle) for 10-second timer
   - Press Button A (right) for 5-second timer
   - Press any button during countdown to interrupt
   - Press any button when finished to reset

## Project Success

This project successfully demonstrates:
- **Automated Development**: Complete code generation and project setup
- **LEIA Framework Application**: Practical implementation of declarative UI principles
- **Wio Terminal Integration**: Proper use of hardware capabilities and constraints
- **User-Centered Design**: Intuitive interface with clear visual feedback
- **Professional Documentation**: Comprehensive guides and technical documentation

The implementation is ready for compilation and deployment to the Wio Terminal hardware.
