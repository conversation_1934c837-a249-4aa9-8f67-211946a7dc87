# Wio Terminal Countdown Timer Project Todo List

## Project Overview
Create a Wio Terminal countdown timer application using LEIA declarative UI framework with JSON-based interface definition.

## Todo Items

### Phase 1: Research and Setup
- [√] Research LEIA framework structure and JSON format from GitHub repository
- [√] Research Wio Terminal button documentation from Seeed Studio wiki
- [√] Create project directory structure according to rules
- [√] Set up Arduino CLI environment for Wio Terminal compilation (successfully configured)

### Phase 2: Knowledge Base Development
- [√] Document LEIA framework basics (states, elements, actions) in knowledge base
- [√] Document Wio Terminal button usage patterns in knowledge base
- [√] Document ArduinoJson library usage for JSON parsing in knowledge base

### Phase 3: Code Development
- [√] Create JSON UI definition following LEIA declarative style
- [√] Implement Arduino .ino code with:
  - [√] JSON parsing using ArduinoJson library (prepared for future use)
  - [√] TFT display initialization and management
  - [√] Button handling (WIO_KEY_A/B/C for 5/10/30 second timers)
  - [√] Countdown timer logic with interrupt capability
  - [√] UI rendering based on JSON definition (implemented as display functions)

### Phase 4: Testing and Deployment
- [√] Compile code using Arduino CLI (successfully compiled - 62,616 bytes, 12% flash usage)
- [√] Deploy to Wio Terminal device (successfully uploaded and verified)
- [!] **ISSUE: Display shows black screen - troubleshooting in progress**
- [√] Created diagnostic tools for display testing
- [√] Enhanced display initialization with proper delays
- [/] Test button functionality and display output (pending display fix)
- [/] Test countdown timers (5s, 10s, 30s) (pending display fix)
- [/] Test interrupt functionality during countdown (pending display fix)

### Phase 4.1: Display Troubleshooting (Current Focus)
- [√] Created comprehensive display diagnostic tool
- [√] Created Wio Terminal specific test with backlight control
- [√] Enhanced original code with proper initialization delays
- [√] Installed LovyanGFX library as alternative solution
- [/] **NEXT: Upload diagnostic tools to identify root cause**

### Phase 5: Documentation
- [√] Create usage instructions
- [√] Update README.md with project description
- [√] Finalize knowledge base entries

## Notes
- Button order: C-B-A (left to right) = 30s-10s-5s
- Use INPUT_PULLUP mode with LOW level detection
- 200ms debounce handling
- All text in English
- Keep UI simple and aesthetic
- Ensure text fits on screen properly
