# ArduinoJson Library Usage for Embedded UI

## Overview
ArduinoJson is a C++ JSON library for Arduino and IoT devices. It's particularly useful for parsing JSON configuration files for dynamic UI generation in embedded systems.

## Basic Usage

### Include Library
```cpp
#include <ArduinoJson.h>
```

### Parsing JSON
```cpp
// Create JSON document
DynamicJsonDocument doc(1024);

// Parse JSON string
const char* json = "{\"type\":\"button\",\"label\":\"Click Me\"}";
DeserializationError error = deserializeJson(doc, json);

if (error) {
    Serial.print("JSON parsing failed: ");
    Serial.println(error.c_str());
    return;
}

// Access values
const char* type = doc["type"];
const char* label = doc["label"];
```

### Working with Arrays
```cpp
// Parse array of elements
JsonArray elements = doc["elements"];
for (JsonObject element : elements) {
    const char* type = element["type"];
    const char* id = element["id"];
    // Process each element
}
```

### Memory Management
```cpp
// Static allocation (faster, fixed size)
StaticJsonDocument<1024> doc;

// Dynamic allocation (flexible size)
DynamicJsonDocument doc(1024);

// Calculate required size
size_t capacity = JSON_OBJECT_SIZE(3) + JSON_ARRAY_SIZE(2) + 60;
DynamicJsonDocument doc(capacity);
```

## LEIA UI Parsing Example

### JSON Structure
```json
{
  "screen_id": "main_screen",
  "elements": [
    {
      "type": "label",
      "id": "title",
      "text": "Hello World",
      "x": 10,
      "y": 20
    },
    {
      "type": "button",
      "id": "btn1",
      "label": "Click",
      "x": 50,
      "y": 100,
      "action_id": "BUTTON_CLICK"
    }
  ]
}
```

### Parsing Implementation
```cpp
void parseUIDefinition(const char* jsonString) {
    DynamicJsonDocument doc(2048);
    
    DeserializationError error = deserializeJson(doc, jsonString);
    if (error) {
        Serial.println("Failed to parse UI JSON");
        return;
    }
    
    const char* screenId = doc["screen_id"];
    JsonArray elements = doc["elements"];
    
    for (JsonObject element : elements) {
        const char* type = element["type"];
        
        if (strcmp(type, "label") == 0) {
            renderLabel(element);
        } else if (strcmp(type, "button") == 0) {
            renderButton(element);
        }
    }
}

void renderLabel(JsonObject element) {
    const char* text = element["text"];
    int x = element["x"];
    int y = element["y"];
    
    tft.drawString(text, x, y, 2);
}

void renderButton(JsonObject element) {
    const char* label = element["label"];
    int x = element["x"];
    int y = element["y"];
    
    // Draw button rectangle and text
    tft.fillRoundRect(x, y, 80, 30, 5, TFT_BLUE);
    tft.setTextColor(TFT_WHITE);
    tft.drawString(label, x + 10, y + 8, 2);
}
```

## Best Practices

### 1. Memory Efficiency
- Use StaticJsonDocument for known JSON sizes
- Calculate exact memory requirements when possible
- Free memory after parsing if using DynamicJsonDocument

### 2. Error Handling
- Always check DeserializationError
- Validate required fields exist
- Provide fallback values for missing optional fields

### 3. Performance
- Parse JSON once, cache results
- Use const char* for string comparisons
- Minimize dynamic memory allocation

### 4. Embedded Constraints
- Keep JSON files small (< 2KB recommended)
- Use abbreviated field names to save space
- Consider binary formats (CBOR) for very constrained systems

## Integration with LEIA Framework

### UI State Management
```cpp
struct UIState {
    String currentScreen;
    JsonObject currentElements;
    bool needsRedraw;
};

UIState uiState;

void updateUI(const char* newJsonConfig) {
    DynamicJsonDocument doc(2048);
    deserializeJson(doc, newJsonConfig);
    
    uiState.currentScreen = doc["screen_id"].as<String>();
    uiState.currentElements = doc["elements"];
    uiState.needsRedraw = true;
}
```

### Dynamic UI Updates
```cpp
void loop() {
    if (uiState.needsRedraw) {
        renderCurrentScreen();
        uiState.needsRedraw = false;
    }
    
    // Handle user input and update UI as needed
}
```

This approach enables dynamic, JSON-driven UI generation while maintaining efficient memory usage on resource-constrained embedded devices.
