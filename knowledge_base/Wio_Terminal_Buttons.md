# Wio Terminal Button Usage

## Button Definitions
- **WIO_KEY_A**: Right button (when looking at screen)
- **WIO_KEY_B**: Middle button
- **WIO_KEY_C**: Left button

## Physical Layout
From left to right on top of Wio Terminal: **C - B - A**

## Configuration
```cpp
void setup() {
    pinMode(WIO_KEY_A, INPUT_PULLUP);
    pinMode(WIO_KEY_B, INPUT_PULLUP);
    pinMode(WIO_KEY_C, INPUT_PULLUP);
}
```

## Reading Button States
```cpp
void loop() {
    if (digitalRead(WIO_KEY_A) == LOW) {
        // Button A pressed
    }
    else if (digitalRead(WIO_KEY_B) == LOW) {
        // Button B pressed
    }
    else if (digitalRead(WIO_KEY_C) == LOW) {
        // Button C pressed
    }
    delay(200); // Simple debounce
}
```

## Key Points
1. **INPUT_PULLUP mode**: Buttons are active LOW
2. **LOW level detection**: Pressed state = LOW
3. **Debounce**: Use 200ms delay for simple debouncing
4. **Alternative names**: Can also use BUTTON_1, BUTTON_2, BUTTON_3

## For Countdown Timer Project
- **WIO_KEY_C (Left)**: 30 second timer
- **WIO_KEY_B (Middle)**: 10 second timer  
- **WIO_KEY_A (Right)**: 5 second timer

This matches the left-to-right order with decreasing time values.
