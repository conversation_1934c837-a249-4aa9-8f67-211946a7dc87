# LEIA Framework Basics

## Overview
LEIA (LLM-driven Embedded Interface Architecture) is a declarative UI framework that uses JSON configuration to define user interfaces for embedded systems.

## Core JSON Structure

### Basic Schema Components

1. **screen_id**: Unique identifier for the screen
2. **background_color**: Hex color code for background
3. **layout**: Defines how elements are arranged
4. **elements**: Array of UI components

### Layout System
```json
"layout": {
  "type": "column",           // or "row"
  "padding": 20,              // padding in pixels
  "gap": 15,                  // spacing between elements
  "item_alignment": [         // alignment options
    "center_horizontal"
  ],
  "distribution": "center"    // distribution method
}
```

### Element Types

#### Label
```json
{
  "type": "label",
  "id": "unique_id",
  "text": "Display text",
  "style_ref": "theme.font.large_title",
  "text_color": "#37474F",
  "horizontal_alignment": "center"
}
```

#### Button
```json
{
  "type": "button",
  "id": "button_id",
  "label": "Button Text",
  "style_ref": "theme.button.primary",
  "action_id": "ACTION_IDENTIFIER"
}
```

#### Container
```json
{
  "type": "container",
  "id": "container_id",
  "style_ref": "theme.card",
  "padding": "15px",
  "width": "90%",
  "layout": {
    "type": "column",
    "gap": 8,
    "item_alignment": ["center_horizontal"]
  },
  "elements": [
    // nested elements
  ]
}
```

### Style References
- `theme.font.large_title`: Large title text
- `theme.font.normal_text`: Normal body text
- `theme.button.primary`: Primary button style
- `theme.card`: Card container style

### Action System
- `action_id`: Links UI events to predefined firmware actions
- Actions must be implemented in the embedded device firmware

## Key Principles
1. **Declarative**: UI is described, not programmed
2. **Hierarchical**: Elements can contain other elements
3. **Styled**: Uses theme-based styling system
4. **Interactive**: Actions link UI events to device functions
