# Wio Terminal Display Troubleshooting Guide

## Current Issue: Black Screen

The display remains black despite successful code compilation and upload. This guide provides systematic troubleshooting steps.

## Step-by-Step Troubleshooting

### Step 1: Hardware Check
**Before testing software, verify hardware:**

1. **Power Check:**
   - Ensure Wio Terminal power switch is ON
   - Check if built-in LED lights up when powered
   - Verify USB connection is secure

2. **Display Connection:**
   - The display is built-in, but check for any visible damage
   - Ensure no loose connections (this is rare with built-in displays)

### Step 2: Upload Diagnostic Tool

**Use Arduino IDE (recommended) or CLI to upload one of these diagnostic tools:**

#### Option A: Basic Diagnostic
- **File:** `projects/display_diagnostic/display_diagnostic.ino`
- **Purpose:** Comprehensive display testing with serial output
- **What it does:**
  - Tests display initialization
  - Cycles through colors (red, green, blue, white, black)
  - Tests text rendering
  - Draws geometric shapes
  - Provides detailed serial output

#### Option B: Wio Terminal Specific Test
- **File:** `projects/wio_specific_test/wio_specific_test.ino`
- **Purpose:** Uses Wio Terminal specific initialization
- **What it does:**
  - Explicitly controls backlight
  - Tests buttons A/B/C
  - Uses Wio Terminal specific pin definitions
  - Provides button feedback

### Step 3: Upload Process

#### Method 1: Arduino IDE (Recommended)
1. Open Arduino IDE
2. Load diagnostic sketch
3. Select **Tools > Board > Seeeduino Wio Terminal**
4. Select **Tools > Port > /dev/cu.usbmodem101**
5. Click **Upload**

#### Method 2: Bootloader Mode (if upload fails)
1. **Put Wio Terminal in bootloader mode:**
   - Slide power switch to ON
   - Press and hold **BOOT** button (small button near USB)
   - Press and release **RESET** button (while holding BOOT)
   - Release **BOOT** button
   - LED should change pattern (indicating bootloader mode)
2. Try upload again immediately

### Step 4: Monitor Results

#### Serial Monitor Check
1. Open Serial Monitor (115200 baud)
2. You should see detailed output like:
   ```
   === WIO TERMINAL DISPLAY DIAGNOSTIC ===
   Starting comprehensive display test...
   Test 1: Basic TFT initialization...
     - tft.init() completed
   Test 2: Setting rotation...
   ```

#### Visual Check
**What you should see on screen:**
- Color cycles: Red → Green → Blue → White → Black
- Text: "WIO TERMINAL", "DISPLAY TEST"
- Geometric shapes: colored rectangles and circles
- Blinking green/red indicator circle

### Step 5: Interpret Results

#### If Serial Output Shows but Screen is Black:
- **Possible Cause:** Backlight issue or display controller problem
- **Solution:** Try the Wio Terminal specific test which explicitly controls backlight
- **Hardware Issue:** Display may need replacement

#### If No Serial Output:
- **Possible Cause:** Code not uploading or running
- **Solution:** Try bootloader mode upload
- **Check:** Verify correct board and port selection

#### If Partial Display Works:
- **Some colors show:** Display hardware is working, initialization timing issue
- **Text but no colors:** Color depth or initialization problem
- **Colors but no text:** Font/text rendering issue

### Step 6: Alternative Solutions

#### If Diagnostic Tools Don't Work:

1. **Try Different TFT_eSPI Configuration:**
   - Check if TFT_eSPI library needs specific Wio Terminal setup
   - Look for User_Setup.h configuration file

2. **Use LovyanGFX Library:**
   - We have LovyanGFX installed as alternative
   - Known to work better with some Wio Terminal units

3. **Check Library Versions:**
   - Ensure TFT_eSPI is compatible version
   - Try updating/downgrading if needed

### Step 7: Hardware Verification

#### If All Software Tests Fail:

1. **Check Built-in LED:**
   - Should light up when powered
   - Should blink during diagnostic tests

2. **Test Buttons:**
   - Wio Terminal specific test shows button presses
   - Buttons should respond in serial output

3. **USB Communication:**
   - Serial monitor should show output
   - Indicates MCU is working

## Expected Diagnostic Results

### Successful Display Test:
- **Serial Output:** Complete test sequence with no errors
- **Visual:** Color cycles, text, shapes, blinking indicator
- **Conclusion:** Display hardware is working

### Failed Display Test:
- **Serial Output:** Test messages but no visual changes
- **Visual:** Screen remains black
- **Conclusion:** Display hardware or initialization issue

## Next Steps Based on Results

### If Diagnostic Shows Working Display:
- Return to countdown timer code
- Issue was likely initialization timing
- Use enhanced version with proper delays

### If Diagnostic Shows Black Screen:
- Try LovyanGFX alternative
- Check hardware connections
- Consider hardware replacement

## Files for Testing
1. `projects/display_diagnostic/display_diagnostic.ino` - Comprehensive test
2. `projects/wio_specific_test/wio_specific_test.ino` - Wio Terminal specific
3. `projects/display_test/display_test.ino` - Simple test
4. `projects/countdown_timer/countdown_timer.ino` - Enhanced countdown timer

**Start with the diagnostic tools to identify the root cause of the black screen issue.**
