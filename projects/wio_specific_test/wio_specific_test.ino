/*
 * Wio Terminal Specific Display Test
 * Uses Wio Terminal specific libraries and initialization
 */

// Try using <PERSON><PERSON>'s specific TFT library first
#include "TFT_eSPI.h"

TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  delay(3000);
  
  Serial.println("=== WIO TERMINAL SPECIFIC TEST ===");
  
  // Initialize built-in LED
  pinMode(LED_BUILTIN, OUTPUT);
  digitalWrite(LED_BUILTIN, HIGH);
  Serial.println("Built-in LED should be ON");
  
  // Test buttons first
  Serial.println("Testing buttons...");
  pinMode(WIO_KEY_A, INPUT_PULLUP);
  pinMode(WIO_KEY_B, INPUT_PULLUP);
  pinMode(WIO_KEY_C, INPUT_PULLUP);
  Serial.println("Buttons initialized");
  
  // Initialize display with Wio Terminal specific sequence
  Serial.println("Initializing display with Wio Terminal sequence...");
  
  // Power on display
  pinMode(LCD_BACKLIGHT, OUTPUT);
  digitalWrite(LCD_BACKLIGHT, HIGH);
  Serial.println("Backlight turned ON");
  delay(100);
  
  // Initialize TFT
  tft.init();
  Serial.println("TFT initialized");
  delay(200);
  
  // Set rotation for landscape
  tft.setRotation(3);
  Serial.println("Rotation set to landscape");
  delay(100);
  
  // Test with bright colors
  Serial.println("Testing bright colors...");
  
  tft.fillScreen(TFT_RED);
  Serial.println("RED screen");
  delay(1000);
  
  tft.fillScreen(TFT_GREEN);
  Serial.println("GREEN screen");
  delay(1000);
  
  tft.fillScreen(TFT_BLUE);
  Serial.println("BLUE screen");
  delay(1000);
  
  tft.fillScreen(TFT_WHITE);
  Serial.println("WHITE screen");
  delay(1000);
  
  // Test text
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(3);
  tft.setCursor(50, 50);
  tft.print("WIO TERMINAL");
  
  tft.setTextSize(2);
  tft.setTextColor(TFT_GREEN);
  tft.setCursor(50, 100);
  tft.print("DISPLAY TEST");
  
  tft.setTextSize(1);
  tft.setTextColor(TFT_YELLOW);
  tft.setCursor(50, 140);
  tft.print("Press buttons A/B/C to test");
  
  Serial.println("Display test complete!");
  Serial.println("You should see:");
  Serial.println("- White 'WIO TERMINAL' text");
  Serial.println("- Green 'DISPLAY TEST' text");
  Serial.println("- Yellow instruction text");
  Serial.println("- Built-in LED should be ON");
}

void loop() {
  static unsigned long lastCheck = 0;
  static int testCounter = 0;
  
  if (millis() - lastCheck > 500) {
    // Test buttons
    bool buttonA = digitalRead(WIO_KEY_A) == LOW;
    bool buttonB = digitalRead(WIO_KEY_B) == LOW;
    bool buttonC = digitalRead(WIO_KEY_C) == LOW;
    
    if (buttonA || buttonB || buttonC) {
      tft.fillRect(0, 180, 320, 40, TFT_BLACK);
      tft.setTextColor(TFT_CYAN);
      tft.setTextSize(2);
      tft.setCursor(10, 190);
      
      if (buttonA) {
        tft.print("Button A Pressed!");
        Serial.println("Button A pressed");
      } else if (buttonB) {
        tft.print("Button B Pressed!");
        Serial.println("Button B pressed");
      } else if (buttonC) {
        tft.print("Button C Pressed!");
        Serial.println("Button C pressed");
      }
    }
    
    // Blink indicator
    testCounter++;
    bool state = (testCounter % 2) == 0;
    tft.fillCircle(300, 30, 10, state ? TFT_GREEN : TFT_RED);
    digitalWrite(LED_BUILTIN, state);
    
    Serial.print("Test cycle #");
    Serial.println(testCounter);
    
    lastCheck = millis();
  }
}
