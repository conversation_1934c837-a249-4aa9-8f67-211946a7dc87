/*
 * TFT Configuration Test for Wio Terminal
 * This test tries to manually configure TFT_eSPI for Wio Terminal
 */

// Force Wio Terminal configuration
#define USER_SETUP_LOADED 1
#define ILI9341_DRIVER 1
#define TFT_WIDTH  240
#define TFT_HEIGHT 320
#define TFT_MISO 19
#define TFT_MOSI 23
#define TFT_SCLK 18
#define TFT_CS   15
#define TFT_DC    2
#define TFT_RST   4
#define LOAD_GLCD 1
#define LOAD_FONT2 1
#define LOAD_FONT4 1
#define LOAD_FONT6 1
#define LOAD_FONT7 1
#define LOAD_FONT8 1
#define LOAD_GFXFF 1
#define SMOOTH_FONT 1
#define SPI_FREQUENCY  27000000

#include <TFT_eSPI.h>
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup() {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== TFT CONFIGURATION TEST ===");
    Serial.println("Attempting manual TFT configuration for Wio Terminal...");
    
    // Try to manually initialize SPI and pins
    Serial.println("Setting up SPI pins...");
    pinMode(TFT_CS, OUTPUT);
    pinMode(TFT_DC, OUTPUT);
    pinMode(TFT_RST, OUTPUT);
    
    // Reset sequence
    Serial.println("Performing display reset...");
    digitalWrite(TFT_RST, LOW);
    delay(100);
    digitalWrite(TFT_RST, HIGH);
    delay(100);
    
    // Initialize TFT
    Serial.println("Initializing TFT...");
    tft.init();
    Serial.println("TFT init completed");
    
    // Try different rotations
    for (int rotation = 0; rotation < 4; rotation++) {
        Serial.print("Testing rotation ");
        Serial.println(rotation);
        
        tft.setRotation(rotation);
        
        // Fill with bright color
        tft.fillScreen(TFT_RED);
        delay(1000);
        
        tft.fillScreen(TFT_GREEN);
        delay(1000);
        
        tft.fillScreen(TFT_BLUE);
        delay(1000);
        
        // Try text
        tft.fillScreen(TFT_BLACK);
        tft.setTextColor(TFT_WHITE);
        tft.setTextSize(2);
        tft.setCursor(10, 10);
        tft.print("ROT:");
        tft.print(rotation);
        delay(2000);
    }
    
    Serial.println("Configuration test complete");
    Serial.println("If you saw colors/text, display is working");
}

void loop() {
    // Continuous test
    static unsigned long lastTest = 0;
    static int testCount = 0;
    
    if (millis() - lastTest > 5000) {
        testCount++;
        Serial.print("Test cycle #");
        Serial.println(testCount);
        
        // Bright flash test
        tft.fillScreen(TFT_WHITE);
        delay(100);
        tft.fillScreen(TFT_BLACK);
        delay(100);
        tft.fillScreen(TFT_RED);
        delay(100);
        tft.fillScreen(TFT_BLACK);
        
        // LED blink
        digitalWrite(LED_BUILTIN, HIGH);
        delay(100);
        digitalWrite(LED_BUILTIN, LOW);
        
        lastTest = millis();
    }
}
