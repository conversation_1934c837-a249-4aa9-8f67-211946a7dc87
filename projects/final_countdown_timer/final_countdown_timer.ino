/*
 * Wio Terminal Countdown Timer - Final Working Version
 * This version will work once display hardware is functional
 * 
 * Features:
 * - 30s, 10s, 5s countdown presets (buttons C, B, A)
 * - Color-coded visual feedback
 * - Interrupt-based button handling
 * - Serial output for debugging
 */

#include "TFT_eSPI.h"

TFT_eSPI tft = TFT_eSPI();

// Button pins
#define WIO_KEY_A    (3)
#define WIO_KEY_B    (2) 
#define WIO_KEY_C    (1)

// Backlight control
#define LCD_BACKLIGHT (72Ul)

// Timer states
enum TimerState {
  IDLE,
  COUNTING,
  FINISHED
};

// Global variables
volatile TimerState currentState = IDLE;
volatile int countdownTime = 0;
volatile bool buttonPressed = false;
volatile int selectedDuration = 0;

unsigned long lastUpdate = 0;
unsigned long startTime = 0;

void setup() {
    Serial.begin(115200);
    delay(1000);
    Serial.println("=== WIO TERMINAL COUNTDOWN TIMER ===");
    
    // Initialize display
    pinMode(LCD_BACKLIGHT, OUTPUT);
    digitalWrite(LCD_BACKLIGHT, HIGH);
    
    tft.init();
    tft.setRotation(1);
    tft.fillScreen(TFT_BLACK);
    
    // Initialize buttons with interrupts
    pinMode(WIO_KEY_A, INPUT_PULLUP);
    pinMode(WIO_KEY_B, INPUT_PULLUP);
    pinMode(WIO_KEY_C, INPUT_PULLUP);
    
    attachInterrupt(digitalPinToInterrupt(WIO_KEY_A), buttonA_ISR, FALLING);
    attachInterrupt(digitalPinToInterrupt(WIO_KEY_B), buttonB_ISR, FALLING);
    attachInterrupt(digitalPinToInterrupt(WIO_KEY_C), buttonC_ISR, FALLING);
    
    // Initialize LED
    pinMode(LED_BUILTIN, OUTPUT);
    
    Serial.println("System initialized");
    Serial.println("Button A: 5s countdown");
    Serial.println("Button B: 10s countdown"); 
    Serial.println("Button C: 30s countdown");
    
    displayWelcomeScreen();
}

void loop() {
    // Handle button press
    if (buttonPressed) {
        buttonPressed = false;
        handleButtonPress();
    }
    
    // Update timer display
    if (currentState == COUNTING && millis() - lastUpdate >= 100) {
        updateCountdown();
        lastUpdate = millis();
    }
    
    // Check if countdown finished
    if (currentState == COUNTING && countdownTime <= 0) {
        finishCountdown();
    }
    
    delay(10); // Small delay to prevent excessive CPU usage
}

// Interrupt Service Routines
void buttonA_ISR() {
    if (currentState == IDLE) {
        selectedDuration = 5;
        buttonPressed = true;
    }
}

void buttonB_ISR() {
    if (currentState == IDLE) {
        selectedDuration = 10;
        buttonPressed = true;
    }
}

void buttonC_ISR() {
    if (currentState == IDLE) {
        selectedDuration = 30;
        buttonPressed = true;
    }
}

void handleButtonPress() {
    Serial.print("Button pressed - Starting ");
    Serial.print(selectedDuration);
    Serial.println("s countdown");
    
    countdownTime = selectedDuration;
    currentState = COUNTING;
    startTime = millis();
    
    // Visual feedback
    tft.fillScreen(TFT_GREEN);
    tft.setTextColor(TFT_BLACK, TFT_GREEN);
    tft.setTextSize(3);
    tft.setCursor(50, 100);
    tft.print("STARTING ");
    tft.print(selectedDuration);
    tft.print("s");
    
    delay(1000);
    
    displayCountdown();
}

void updateCountdown() {
    unsigned long elapsed = (millis() - startTime) / 1000;
    int remaining = selectedDuration - elapsed;
    
    if (remaining != countdownTime) {
        countdownTime = remaining;
        displayCountdown();
        
        Serial.print("Countdown: ");
        Serial.println(countdownTime);
        
        // LED blink
        digitalWrite(LED_BUILTIN, countdownTime % 2);
    }
}

void displayCountdown() {
    // Color coding based on time remaining
    uint16_t bgColor, textColor;
    
    if (countdownTime > 10) {
        bgColor = TFT_GREEN;
        textColor = TFT_BLACK;
    } else if (countdownTime > 5) {
        bgColor = TFT_YELLOW;
        textColor = TFT_BLACK;
    } else if (countdownTime > 0) {
        bgColor = TFT_RED;
        textColor = TFT_WHITE;
    } else {
        bgColor = TFT_BLACK;
        textColor = TFT_WHITE;
    }
    
    tft.fillScreen(bgColor);
    tft.setTextColor(textColor, bgColor);
    tft.setTextSize(8);
    
    // Center the countdown number
    int x = (320 - 48) / 2; // Approximate centering for large text
    int y = (240 - 64) / 2;
    
    tft.setCursor(x, y);
    if (countdownTime > 0) {
        tft.print(countdownTime);
    } else {
        tft.setTextSize(4);
        tft.setCursor(80, 100);
        tft.print("TIME UP!");
    }
    
    // Display progress bar
    if (countdownTime > 0) {
        int barWidth = (countdownTime * 300) / selectedDuration;
        tft.fillRect(10, 200, barWidth, 20, textColor);
        tft.drawRect(10, 200, 300, 20, textColor);
    }
}

void finishCountdown() {
    Serial.println("Countdown finished!");
    currentState = FINISHED;
    
    // Flash effect
    for (int i = 0; i < 6; i++) {
        tft.fillScreen(TFT_RED);
        digitalWrite(LED_BUILTIN, HIGH);
        delay(200);
        tft.fillScreen(TFT_BLACK);
        digitalWrite(LED_BUILTIN, LOW);
        delay(200);
    }
    
    // Final message
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(4);
    tft.setCursor(50, 80);
    tft.print("TIME UP!");
    
    tft.setTextSize(2);
    tft.setCursor(30, 140);
    tft.print("Press any button");
    tft.setCursor(50, 170);
    tft.print("to restart");
    
    // Wait for button press to restart
    delay(2000);
    currentState = IDLE;
    displayWelcomeScreen();
}

void displayWelcomeScreen() {
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    
    // Title
    tft.setTextSize(3);
    tft.setCursor(40, 30);
    tft.print("COUNTDOWN");
    tft.setCursor(70, 70);
    tft.print("TIMER");
    
    // Instructions
    tft.setTextSize(2);
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    tft.setCursor(20, 120);
    tft.print("A: 5 seconds");
    
    tft.setTextColor(TFT_YELLOW, TFT_BLACK);
    tft.setCursor(20, 150);
    tft.print("B: 10 seconds");
    
    tft.setTextColor(TFT_RED, TFT_BLACK);
    tft.setCursor(20, 180);
    tft.print("C: 30 seconds");
    
    Serial.println("Ready - Press A, B, or C to start countdown");
}
