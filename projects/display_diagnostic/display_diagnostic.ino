/*
 * Wio Terminal Display Diagnostic Tool
 * This tool systematically tests display initialization and hardware
 */

#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  delay(3000); // Give plenty of time for serial to initialize
  
  Serial.println("=== WIO TERMINAL DISPLAY DIAGNOSTIC ===");
  Serial.println("Starting comprehensive display test...");
  
  // Test 1: Basic initialization
  Serial.println("Test 1: Basic TFT initialization...");
  tft.init();
  Serial.println("  - tft.init() completed");
  delay(500);
  
  // Test 2: Rotation setting
  Serial.println("Test 2: Setting rotation...");
  tft.setRotation(0); // Portrait first
  Serial.println("  - Portrait mode set");
  delay(500);
  
  // Test 3: Fill screen with different colors
  Serial.println("Test 3: Testing screen fill...");
  
  Serial.println("  - Filling with RED...");
  tft.fillScreen(TFT_RED);
  delay(2000);
  
  Serial.println("  - Filling with GREEN...");
  tft.fillScreen(TFT_GREEN);
  delay(2000);
  
  Serial.println("  - Filling with BLUE...");
  tft.fillScreen(TFT_BLUE);
  delay(2000);
  
  Serial.println("  - Filling with WHITE...");
  tft.fillScreen(TFT_WHITE);
  delay(2000);
  
  Serial.println("  - Filling with BLACK...");
  tft.fillScreen(TFT_BLACK);
  delay(1000);
  
  // Test 4: Try landscape mode
  Serial.println("Test 4: Switching to landscape mode...");
  tft.setRotation(3);
  Serial.println("  - Landscape mode set");
  
  tft.fillScreen(TFT_NAVY);
  delay(1000);
  
  // Test 5: Text rendering
  Serial.println("Test 5: Testing text rendering...");
  tft.setTextColor(TFT_WHITE, TFT_NAVY);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("Size 1 Text");
  
  tft.setTextSize(2);
  tft.setCursor(0, 20);
  tft.println("Size 2 Text");
  
  tft.setTextSize(3);
  tft.setCursor(0, 50);
  tft.println("Size 3");
  
  Serial.println("  - Text rendering completed");
  delay(2000);
  
  // Test 6: Geometric shapes
  Serial.println("Test 6: Testing geometric shapes...");
  tft.fillScreen(TFT_BLACK);
  
  // Draw rectangles
  tft.fillRect(10, 10, 50, 30, TFT_RED);
  tft.fillRect(70, 10, 50, 30, TFT_GREEN);
  tft.fillRect(130, 10, 50, 30, TFT_BLUE);
  
  // Draw circles
  tft.fillCircle(35, 70, 20, TFT_YELLOW);
  tft.fillCircle(95, 70, 20, TFT_CYAN);
  tft.fillCircle(155, 70, 20, TFT_MAGENTA);
  
  Serial.println("  - Geometric shapes completed");
  delay(3000);
  
  // Test 7: Final status display
  Serial.println("Test 7: Final status display...");
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_GREEN);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("DISPLAY TEST");
  tft.setCursor(10, 40);
  tft.println("COMPLETE!");
  
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(10, 80);
  tft.println("If you can see this text,");
  tft.setCursor(10, 100);
  tft.println("your display is working.");
  
  tft.setCursor(10, 130);
  tft.println("Check Serial Monitor for");
  tft.setCursor(10, 150);
  tft.println("detailed test results.");
  
  Serial.println("=== DIAGNOSTIC COMPLETE ===");
  Serial.println("If you can see colors and text on screen,");
  Serial.println("the display hardware is working correctly.");
  Serial.println("If screen is still black, there may be a");
  Serial.println("hardware connection issue.");
}

void loop() {
  // Blink indicator to show program is running
  static unsigned long lastBlink = 0;
  static bool ledState = false;
  static int blinkCount = 0;
  
  if (millis() - lastBlink > 1000) {
    ledState = !ledState;
    
    // Blink a small indicator on screen
    tft.fillCircle(300, 20, 8, ledState ? TFT_GREEN : TFT_BLACK);
    
    // Also blink built-in LED if available
    digitalWrite(LED_BUILTIN, ledState);
    
    blinkCount++;
    Serial.print("Blink #");
    Serial.print(blinkCount);
    Serial.println(ledState ? " ON" : " OFF");
    
    lastBlink = millis();
  }
}
