/*
 * Minimal Wio Terminal Test - Exact Reference Pattern
 * Using the exact initialization from RLE_Font_test.ino
 */

#include <TFT_eSPI.h>
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup(void) {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== MINIMAL WIO TERMINAL TEST ===");
    Serial.println("Initializing display...");
    
    // Exact initialization from reference
    tft.init();
    tft.setRotation(1);
    
    Serial.println("Display initialized");
    Serial.println("Testing basic fill...");
    
    // Test basic screen fill - this should work if hardware is OK
    tft.fillScreen(TFT_RED);
    Serial.println("Screen should be RED now");
    delay(3000);
    
    tft.fillScreen(TFT_GREEN);
    Serial.println("Screen should be GREEN now");
    delay(3000);
    
    tft.fillScreen(TFT_BLUE);
    Serial.println("Screen should be BLUE now");
    delay(3000);
    
    tft.fillScreen(TFT_WHITE);
    Serial.println("Screen should be WHITE now");
    delay(3000);
    
    tft.fillScreen(TFT_BLACK);
    Serial.println("Screen should be BLACK now");
    delay(1000);
    
    // Test text
    Serial.println("Testing text rendering...");
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.drawString("HELLO", 10, 10, 4);
    Serial.println("Should see 'HELLO' in white text");
    
    Serial.println("=== TEST COMPLETE ===");
    Serial.println("If screen is still black, there may be a hardware issue");
}

void loop() {
    // Blink built-in LED to show program is running
    static unsigned long lastBlink = 0;
    static bool ledState = false;
    
    if (millis() - lastBlink > 1000) {
        ledState = !ledState;
        digitalWrite(LED_BUILTIN, ledState);
        Serial.print("LED blink - ");
        Serial.println(ledState ? "ON" : "OFF");
        lastBlink = millis();
    }
}
