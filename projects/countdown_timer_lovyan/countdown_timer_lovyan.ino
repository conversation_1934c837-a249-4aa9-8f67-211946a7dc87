/*
 * Wio Terminal Countdown Timer - LovyanGFX Version
 * Using LEIA-inspired declarative UI with JSON parsing
 * 
 * Features:
 * - Three countdown options: 30s, 10s, 5s
 * - Physical buttons C/B/A correspond to 30s/10s/5s
 * - Visual button hints at top of screen
 * - Large countdown display
 * - Interrupt capability during countdown
 * 
 * Hardware: Seeed Wio Terminal
 * Libraries: LovyanGFX, ArduinoJson
 */

#include <LovyanGFX.hpp>
#include <ArduinoJson.h>

// LovyanGFX setup for Wio Terminal
class LGFX : public lgfx::LGFX_Device
{
  lgfx::Panel_ILI9341 _panel_instance;
  lgfx::Bus_SPI _bus_instance;
  lgfx::Light_PWM _light_instance;

public:
  LGFX(void)
  {
    {
      auto cfg = _bus_instance.config();
      cfg.spi_host = SPI_HOST;
      cfg.spi_mode = 0;
      cfg.freq_write = 40000000;
      cfg.freq_read = 16000000;
      cfg.spi_3wire = true;
      cfg.use_lock = true;
      cfg.dma_channel = SPI_DMA_CH_AUTO;
      cfg.pin_sclk = LCD_SCK;
      cfg.pin_mosi = LCD_MOSI;
      cfg.pin_miso = LCD_MISO;
      cfg.pin_dc = LCD_DC;
      _bus_instance.config(cfg);
      _panel_instance.setBus(&_bus_instance);
    }

    {
      auto cfg = _panel_instance.config();
      cfg.pin_cs = LCD_SS_PIN;
      cfg.pin_rst = LCD_RESET;
      cfg.pin_busy = -1;
      cfg.memory_width = 240;
      cfg.memory_height = 320;
      cfg.panel_width = 240;
      cfg.panel_height = 320;
      cfg.offset_x = 0;
      cfg.offset_y = 0;
      cfg.offset_rotation = 0;
      cfg.dummy_read_pixel = 8;
      cfg.dummy_read_bits = 1;
      cfg.readable = true;
      cfg.invert = true;
      cfg.rgb_order = false;
      cfg.dlen_16bit = false;
      cfg.bus_shared = true;
      _panel_instance.config(cfg);
    }

    {
      auto cfg = _light_instance.config();
      cfg.pin_bl = LCD_BACKLIGHT;
      cfg.invert = false;
      cfg.freq = 44100;
      cfg.pwm_channel = 7;
      _light_instance.config(cfg);
      _panel_instance.setLight(&_light_instance);
    }

    setPanel(&_panel_instance);
  }
};

LGFX tft;

// Button definitions
#define BUTTON_30S WIO_KEY_C  // Left button
#define BUTTON_10S WIO_KEY_B  // Middle button  
#define BUTTON_5S  WIO_KEY_A  // Right button

// Colors
#define COLOR_BLACK    0x0000
#define COLOR_WHITE    0xFFFF
#define COLOR_GREEN    0x07E0
#define COLOR_BLUE     0x001F
#define COLOR_RED      0xF800
#define COLOR_YELLOW   0xFFE0
#define COLOR_ORANGE   0xFD20

// Timer states
enum TimerState {
  READY,
  COUNTING,
  FINISHED
};

// Global variables
TimerState currentState = READY;
unsigned long countdownStart = 0;
int countdownDuration = 0;
int remainingTime = 0;
unsigned long lastButtonPress = 0;
const unsigned long DEBOUNCE_DELAY = 200;

// JSON UI definition (LEIA-style)
const char* uiDefinition = R"({
  "screen_id": "countdown_main",
  "layout": {
    "type": "column",
    "alignment": "center"
  },
  "elements": [
    {
      "type": "container",
      "id": "button_hints",
      "layout": {"type": "row", "alignment": "space_between"},
      "elements": [
        {"type": "label", "id": "btn_c", "text": "C:30s", "color": "blue"},
        {"type": "label", "id": "btn_b", "text": "B:10s", "color": "blue"},
        {"type": "label", "id": "btn_a", "text": "A:5s", "color": "blue"}
      ]
    },
    {
      "type": "label",
      "id": "instruction",
      "text": "Press C/B/A for 30/10/5 sec timer",
      "font_size": "medium"
    },
    {
      "type": "label",
      "id": "countdown_display",
      "text": "00",
      "font_size": "large",
      "color": "green"
    },
    {
      "type": "label",
      "id": "status",
      "text": "Ready",
      "font_size": "small"
    }
  ]
})";

void setup() {
  Serial.begin(115200);
  Serial.println("Wio Terminal Countdown Timer Starting...");
  
  // Initialize display
  tft.init();
  tft.setRotation(3); // Landscape mode
  tft.fillScreen(COLOR_BLACK);
  
  // Initialize buttons
  pinMode(BUTTON_30S, INPUT_PULLUP);
  pinMode(BUTTON_10S, INPUT_PULLUP);
  pinMode(BUTTON_5S, INPUT_PULLUP);
  
  // Parse and display initial UI
  parseAndRenderUI();
  
  Serial.println("Setup complete!");
}

void loop() {
  handleButtons();
  updateTimer();
  delay(50); // Small delay to prevent excessive CPU usage
}

void parseAndRenderUI() {
  // Parse JSON UI definition
  DynamicJsonDocument doc(2048);
  DeserializationError error = deserializeJson(doc, uiDefinition);
  
  if (error) {
    Serial.print("JSON parsing failed: ");
    Serial.println(error.c_str());
    return;
  }
  
  // Clear screen
  tft.fillScreen(COLOR_BLACK);
  
  // Render UI elements based on current state
  renderButtonHints();
  renderInstructions();
  renderCountdownDisplay();
  renderStatus();
}

void renderButtonHints() {
  // Draw button hints at top of screen
  tft.setTextColor(COLOR_BLUE);
  tft.setTextSize(2);
  
  // Button C (30s) - Left
  tft.setCursor(20, 20);
  tft.print("C:30s");
  
  // Button B (10s) - Middle  
  tft.setCursor(140, 20);
  tft.print("B:10s");
  
  // Button A (5s) - Right
  tft.setCursor(260, 20);
  tft.print("A:5s");
}

void renderInstructions() {
  if (currentState == READY) {
    tft.setTextColor(COLOR_WHITE);
    tft.setTextSize(2);
    tft.setCursor(10, 80);
    tft.print("Press C/B/A for 30/10/5 sec timer");
  }
}

void renderCountdownDisplay() {
  // Clear countdown area
  tft.fillRect(0, 120, 320, 80, COLOR_BLACK);
  
  if (currentState == COUNTING || currentState == FINISHED) {
    // Choose color based on remaining time
    uint16_t color = COLOR_GREEN;
    if (remainingTime <= 5) {
      color = COLOR_RED;
    } else if (remainingTime <= 10) {
      color = COLOR_ORANGE;
    }
    
    tft.setTextColor(color);
    tft.setTextSize(6); // Large font for countdown
    
    // Center the countdown number
    String timeStr = String(remainingTime);
    int textWidth = timeStr.length() * 36; // Approximate width
    int x = (320 - textWidth) / 2;
    
    tft.setCursor(x, 140);
    tft.print(timeStr);
  }
}

void renderStatus() {
  // Clear status area
  tft.fillRect(0, 220, 320, 40, COLOR_BLACK);
  
  tft.setTextColor(COLOR_WHITE);
  tft.setTextSize(2);
  tft.setCursor(120, 230);
  
  switch (currentState) {
    case READY:
      tft.print("Ready");
      break;
    case COUNTING:
      tft.print("Counting...");
      break;
    case FINISHED:
      tft.print("Finished!");
      break;
  }
}

void handleButtons() {
  unsigned long currentTime = millis();
  
  // Debounce check
  if (currentTime - lastButtonPress < DEBOUNCE_DELAY) {
    return;
  }
  
  bool buttonPressed = false;
  
  // Check buttons
  if (digitalRead(BUTTON_30S) == LOW) {
    startCountdown(30);
    buttonPressed = true;
  } else if (digitalRead(BUTTON_10S) == LOW) {
    startCountdown(10);
    buttonPressed = true;
  } else if (digitalRead(BUTTON_5S) == LOW) {
    startCountdown(5);
    buttonPressed = true;
  }
  
  if (buttonPressed) {
    lastButtonPress = currentTime;
    Serial.println("Button pressed!");
  }
}

void startCountdown(int seconds) {
  Serial.print("Starting countdown: ");
  Serial.print(seconds);
  Serial.println(" seconds");
  
  countdownDuration = seconds;
  remainingTime = seconds;
  countdownStart = millis();
  currentState = COUNTING;
  
  // Update display immediately
  parseAndRenderUI();
}

void updateTimer() {
  if (currentState == COUNTING) {
    unsigned long elapsed = (millis() - countdownStart) / 1000;
    remainingTime = countdownDuration - elapsed;
    
    if (remainingTime <= 0) {
      remainingTime = 0;
      currentState = FINISHED;
      Serial.println("Countdown finished!");
      
      // Auto-return to ready state after 2 seconds
      delay(2000);
      currentState = READY;
      parseAndRenderUI();
    } else {
      // Update display
      renderCountdownDisplay();
      renderStatus();
    }
  }
}
