/*
 * Wio Terminal Countdown Timer
 * Using LEIA-inspired declarative UI with JSON parsing
 * 
 * Features:
 * - Three countdown options: 30s, 10s, 5s
 * - Physical buttons C/B/A correspond to 30s/10s/5s
 * - Visual button hints at top of screen
 * - Large countdown display
 * - Interrupt capability during countdown
 */

#include <TFT_eSPI.h>
#include <ArduinoJson.h>

// Display setup
TFT_eSPI tft = TFT_eSPI();

// Button definitions
#define BUTTON_30S WIO_KEY_C  // Left button
#define BUTTON_10S WIO_KEY_B  // Middle button  
#define BUTTON_5S  WIO_KEY_A  // Right button

// Use TFT_eSPI standard colors (no custom definitions needed)

// Timer states
enum TimerState {
  READY,
  COUNTING,
  FINISHED
};

// Global variables
TimerState currentState = READY;
unsigned long countdownStart = 0;
int countdownDuration = 0;
int remainingTime = 0;
unsigned long lastUpdate = 0;
unsigned long lastButtonCheck = 0;

void setup() {
  Serial.begin(115200);

  // Initialize display using reference pattern
  tft.init();
  tft.setRotation(1);  // Use rotation 1 like in reference (landscape)

  // Initialize buttons
  pinMode(BUTTON_30S, INPUT_PULLUP);
  pinMode(BUTTON_10S, INPUT_PULLUP);
  pinMode(BUTTON_5S, INPUT_PULLUP);

  // Draw initial UI
  drawMainInterface();

  Serial.println("Wio Terminal Countdown Timer Ready");
}

void loop() {
  unsigned long currentTime = millis();
  
  // Check buttons with debounce (every 200ms)
  if (currentTime - lastButtonCheck >= 200) {
    checkButtons();
    lastButtonCheck = currentTime;
  }
  
  // Update countdown display
  if (currentState == COUNTING && currentTime - lastUpdate >= 1000) {
    updateCountdown();
    lastUpdate = currentTime;
  }
}

void checkButtons() {
  if (digitalRead(BUTTON_30S) == LOW) {
    handleButtonPress(30);
  }
  else if (digitalRead(BUTTON_10S) == LOW) {
    handleButtonPress(10);
  }
  else if (digitalRead(BUTTON_5S) == LOW) {
    handleButtonPress(5);
  }
}

void handleButtonPress(int seconds) {
  if (currentState == READY) {
    // Start new countdown
    startCountdown(seconds);
  } else if (currentState == COUNTING) {
    // Interrupt current countdown
    stopCountdown();
  } else if (currentState == FINISHED) {
    // Reset to ready state
    resetTimer();
  }
}

void startCountdown(int seconds) {
  currentState = COUNTING;
  countdownDuration = seconds;
  remainingTime = seconds;
  countdownStart = millis();
  lastUpdate = countdownStart;
  
  Serial.println("Starting " + String(seconds) + "s countdown");
  
  // Update display
  drawCountdownInterface();
  updateCountdownDisplay();
}

void updateCountdown() {
  unsigned long elapsed = (millis() - countdownStart) / 1000;
  remainingTime = countdownDuration - elapsed;
  
  if (remainingTime <= 0) {
    // Countdown finished
    remainingTime = 0;
    currentState = FINISHED;
    Serial.println("Countdown finished!");
    drawFinishedInterface();
  } else {
    updateCountdownDisplay();
  }
}

void stopCountdown() {
  currentState = READY;
  Serial.println("Countdown interrupted");
  drawMainInterface();
}

void resetTimer() {
  currentState = READY;
  drawMainInterface();
}

void drawMainInterface() {
  tft.fillScreen(TFT_BLACK);

  // Draw button hints at top
  drawButtonHints();

  // Draw title
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.drawString("Countdown Timer", 160, 60, 4);

  // Draw instructions
  tft.setTextColor(TFT_CYAN, TFT_BLACK);
  tft.drawString("Press C/B/A for 30/10/5 sec timer", 160, 100, 2);

  // Draw large ready display
  tft.setTextColor(TFT_GREEN, TFT_BLACK);
  tft.drawString("00", 160, 140, 8);

  // Draw status
  tft.setTextColor(TFT_YELLOW, TFT_BLACK);
  tft.drawString("Ready", 160, 200, 4);
}

void drawCountdownInterface() {
  tft.fillScreen(TFT_BLACK);
  drawButtonHints();

  // Draw title
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.drawString("Countdown Timer", 160, 60, 4);

  // Draw instructions for interrupt
  tft.setTextColor(TFT_CYAN, TFT_BLACK);
  tft.drawString("Press any button to stop", 160, 100, 2);

  // Status
  tft.setTextColor(TFT_YELLOW, TFT_BLACK);
  tft.drawString("Counting...", 160, 200, 4);
}

void drawFinishedInterface() {
  tft.fillScreen(TFT_BLACK);
  drawButtonHints();

  // Draw title
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.drawString("Countdown Timer", 160, 60, 4);

  // Draw finished message
  tft.setTextColor(TFT_RED, TFT_BLACK);
  tft.drawString("TIME'S UP!", 160, 100, 4);

  // Draw zero
  tft.setTextColor(TFT_RED, TFT_BLACK);
  tft.drawString("00", 160, 140, 8);

  // Status
  tft.setTextColor(TFT_RED, TFT_BLACK);
  tft.drawString("Finished", 160, 200, 4);
}

void drawButtonHints() {
  // Draw button hint rectangles at top
  // 30s button (blue)
  tft.fillRoundRect(50, 20, 60, 30, 5, TFT_BLUE);
  tft.setTextColor(TFT_WHITE, TFT_BLUE);
  tft.drawString("30s", 80, 30, 2);

  // 10s button (green)
  tft.fillRoundRect(130, 20, 60, 30, 5, TFT_GREEN);
  tft.setTextColor(TFT_WHITE, TFT_GREEN);
  tft.drawString("10s", 160, 30, 2);

  // 5s button (orange)
  tft.fillRoundRect(210, 20, 60, 30, 5, TFT_ORANGE);
  tft.setTextColor(TFT_WHITE, TFT_ORANGE);
  tft.drawString("5s", 240, 30, 2);
}

void updateCountdownDisplay() {
  // Clear countdown area
  tft.fillRect(100, 130, 120, 60, TFT_BLACK);

  // Draw remaining time
  String timeStr = String(remainingTime);
  if (remainingTime < 10) {
    timeStr = "0" + timeStr;
  }

  // Color coding for urgency
  uint16_t color = TFT_GREEN;
  if (remainingTime <= 5) {
    color = TFT_RED;
  } else if (remainingTime <= 10) {
    color = TFT_ORANGE;
  }

  tft.setTextColor(color, TFT_BLACK);
  tft.drawString(timeStr, 160, 140, 8);
}
