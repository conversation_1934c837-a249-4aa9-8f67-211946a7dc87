# Wio Terminal Countdown Timer - Usage Instructions

## Overview
This project implements a countdown timer for the Wio Terminal using a LEIA-inspired declarative UI approach with JSON configuration and Arduino code.

## Hardware Requirements
- Seeed Studio Wio Terminal
- USB-C cable for programming and power

## Software Requirements
- Arduino IDE or Arduino CLI
- Required Libraries:
  - TFT_eSPI (for display)
  - ArduinoJson (for JSON parsing)

## Installation Steps

### 1. Install Arduino Libraries
```bash
# Using Arduino CLI
arduino-cli lib install "TFT_eSPI"
arduino-cli lib install "ArduinoJson"
```

### 2. Upload Code
1. Connect Wio Terminal to computer via USB-C
2. Open `countdown_timer.ino` in Arduino IDE
3. Select board: "Seeed Wio Terminal"
4. Select correct COM port
5. Upload the code

## How to Use

### Button Layout
Looking at the Wio Terminal screen, the three buttons on top are arranged as:
```
[C] [B] [A]
30s 10s  5s
```

### Operation Modes

#### Ready Mode (Initial State)
- Display shows "00" and "Ready"
- Button hints are shown at top of screen
- Press any button to start corresponding countdown

#### Countdown Mode
- Large numbers show remaining time
- Display color changes based on urgency:
  - Green: > 10 seconds
  - Orange: 6-10 seconds  
  - Red: ≤ 5 seconds
- Press any button to interrupt and return to Ready mode

#### Finished Mode
- Display shows "TIME'S UP!" and "00" in red
- Press any button to return to Ready mode

### Button Functions
- **Button C (Left)**: Start 30-second countdown
- **Button B (Middle)**: Start 10-second countdown
- **Button A (Right)**: Start 5-second countdown
- **Any button during countdown**: Stop/interrupt timer
- **Any button when finished**: Reset to ready

## Features
- Visual button hints matching physical button layout
- Large, easy-to-read countdown display
- Color-coded urgency indication
- Simple interrupt capability
- Automatic reset after completion
- 200ms debounce handling for reliable button input

## Troubleshooting
- If display doesn't work: Check TFT_eSPI library configuration
- If buttons don't respond: Verify button pin definitions
- If compilation fails: Ensure all required libraries are installed

## Technical Details
- Uses TFT_eSPI library for display management
- ArduinoJson library for JSON parsing (future extensibility)
- Landscape orientation (rotation 3)
- 320x240 pixel display resolution
- Non-blocking timer implementation using millis()
