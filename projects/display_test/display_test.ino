/*
 * Simple Wio Terminal Display Test
 * This is a minimal test to verify the display is working
 */

#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  delay(2000); // Give time for serial to initialize
  Serial.println("Starting Display Test...");
  
  // Initialize display
  tft.init();
  delay(100);
  tft.setRotation(3);  // Landscape mode
  delay(100);
  
  // Fill screen with color to test
  tft.fillScreen(TFT_RED);
  delay(1000);
  
  tft.fillScreen(TFT_GREEN);
  delay(1000);
  
  tft.fillScreen(TFT_BLUE);
  delay(1000);
  
  tft.fillScreen(TFT_BLACK);
  delay(500);
  
  // Draw some text
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(3);
  tft.setCursor(50, 50);
  tft.print("WIO TERMINAL");
  
  tft.setTextSize(2);
  tft.setCursor(50, 100);
  tft.print("Display Working!");
  
  tft.setTextSize(1);
  tft.setCursor(50, 150);
  tft.print("If you can see this text,");
  tft.setCursor(50, 170);
  tft.print("the display is functioning correctly.");
  
  Serial.println("Display test complete!");
}

void loop() {
  // Blink a simple indicator
  static unsigned long lastBlink = 0;
  static bool state = false;
  
  if (millis() - lastBlink > 1000) {
    state = !state;
    tft.fillCircle(300, 30, 10, state ? TFT_GREEN : TFT_BLACK);
    lastBlink = millis();
    Serial.println(state ? "Blink ON" : "Blink OFF");
  }
}
