/*
 * Wio Terminal Display Test using Seeed Libraries
 * This uses the official Seeed display libraries instead of TFT_eSPI
 */

#include "TFT_eSPI.h"
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();

// Backlight control
#define LCD_BACKLIGHT (72Ul)

void setup() {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== SEEED DISPLAY TEST ===");
    
    // Backlight control
    pinMode(LCD_BACKLIGHT, OUTPUT);
    digitalWrite(LCD_BACKLIGHT, HIGH);
    Serial.println("Backlight ON");
    
    // Try different initialization approach
    Serial.println("Initializing with Seeed approach...");
    
    // Initialize SPI first
    SPI.begin();
    
    // Initialize display
    tft.init();
    tft.setRotation(1);
    
    // Try explicit SPI settings
    tft.setSwapBytes(true);
    
    Serial.println("Testing basic pixel drawing...");
    
    // Test 1: Draw individual pixels
    for (int x = 0; x < 320; x += 10) {
        for (int y = 0; y < 240; y += 10) {
            tft.drawPixel(x, y, TFT_RED);
        }
    }
    delay(2000);
    
    // Test 2: Draw lines
    Serial.println("Testing line drawing...");
    tft.fillScreen(TFT_BLACK);
    for (int i = 0; i < 320; i += 20) {
        tft.drawLine(0, 0, i, 240, TFT_GREEN);
    }
    delay(2000);
    
    // Test 3: Draw rectangles
    Serial.println("Testing rectangle drawing...");
    tft.fillScreen(TFT_BLACK);
    tft.drawRect(10, 10, 100, 50, TFT_BLUE);
    tft.fillRect(50, 50, 100, 50, TFT_YELLOW);
    delay(2000);
    
    // Test 4: Try different color formats
    Serial.println("Testing different color formats...");
    
    // RGB565 colors
    uint16_t red565 = 0xF800;
    uint16_t green565 = 0x07E0;
    uint16_t blue565 = 0x001F;
    
    tft.fillScreen(red565);
    delay(1000);
    tft.fillScreen(green565);
    delay(1000);
    tft.fillScreen(blue565);
    delay(1000);
    
    // Test 5: Text with different methods
    Serial.println("Testing text rendering...");
    tft.fillScreen(TFT_BLACK);
    
    // Method 1: Basic text
    tft.setTextColor(TFT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.print("WIO TERMINAL");
    
    // Method 2: drawString
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    tft.drawString("DISPLAY TEST", 10, 50, 2);
    
    // Method 3: Large font
    tft.setTextColor(TFT_YELLOW, TFT_BLACK);
    tft.drawString("SUCCESS!", 10, 100, 4);
    
    Serial.println("=== SEEED TEST COMPLETE ===");
    Serial.println("Check if you can see colors and text now");
}

void loop() {
    static unsigned long lastTest = 0;
    static int testMode = 0;
    
    if (millis() - lastTest > 3000) {
        testMode = (testMode + 1) % 6;
        
        Serial.print("Test mode: ");
        Serial.println(testMode);
        
        switch (testMode) {
            case 0:
                // Solid colors
                tft.fillScreen(TFT_RED);
                Serial.println("RED screen");
                break;
            case 1:
                tft.fillScreen(TFT_GREEN);
                Serial.println("GREEN screen");
                break;
            case 2:
                tft.fillScreen(TFT_BLUE);
                Serial.println("BLUE screen");
                break;
            case 3:
                tft.fillScreen(TFT_WHITE);
                Serial.println("WHITE screen");
                break;
            case 4:
                tft.fillScreen(TFT_YELLOW);
                Serial.println("YELLOW screen");
                break;
            case 5:
                // Pattern test
                tft.fillScreen(TFT_BLACK);
                for (int i = 0; i < 10; i++) {
                    tft.fillRect(i * 32, i * 24, 32, 24, 
                               (i % 2) ? TFT_WHITE : TFT_BLACK);
                }
                Serial.println("PATTERN test");
                break;
        }
        
        // LED indicator
        digitalWrite(LED_BUILTIN, testMode % 2);
        
        lastTest = millis();
    }
}
