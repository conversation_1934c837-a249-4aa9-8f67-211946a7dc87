/*
 * Wio Terminal Reference Test
 * Based on the working RLE_Font_test.ino from the demo document
 * This should definitely work if the display hardware is functioning
 */

#include <TFT_eSPI.h>
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();  // Invoke library, pins defined in User_Setup.h

void setup(void) {
    Serial.begin(115200);
    delay(2000);
    Serial.println("Starting Wio Terminal Reference Test...");
    
    // Initialize exactly like the reference
    tft.init();
    tft.setRotation(1);
    
    Serial.println("Display initialized, starting test...");
}

void loop() {
    Serial.println("=== DISPLAY TEST CYCLE ===");
    
    // Test 1: Fill screen with colors
    Serial.println("Test 1: Color fills");
    tft.fillScreen(TFT_RED);
    delay(1000);
    
    tft.fillScreen(TFT_GREEN);
    delay(1000);
    
    tft.fillScreen(TFT_BLUE);
    delay(1000);
    
    tft.fillScreen(TFT_BLACK);
    delay(500);
    
    // Test 2: Text rendering like reference
    Serial.println("Test 2: Text rendering");
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    
    tft.drawString("WIO TERMINAL", 0, 0, 4);
    tft.drawString("DISPLAY TEST", 0, 26, 4);
    tft.drawString("Reference Code", 0, 52, 4);
    
    delay(2000);
    
    // Test 3: Large numbers like reference
    Serial.println("Test 3: Large numbers");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_YELLOW, TFT_BLACK);
    
    tft.drawString("0123", 0, 0, 8);
    tft.drawString("4567", 0, 72, 8);
    delay(2000);
    
    tft.fillScreen(TFT_BLACK);
    tft.drawString("890", 0, 0, 8);
    delay(2000);
    
    // Test 4: Button test
    Serial.println("Test 4: Button test");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.drawString("Press buttons A/B/C", 0, 0, 2);
    tft.drawString("Check Serial Monitor", 0, 20, 2);
    
    // Initialize buttons
    pinMode(WIO_KEY_A, INPUT_PULLUP);
    pinMode(WIO_KEY_B, INPUT_PULLUP);
    pinMode(WIO_KEY_C, INPUT_PULLUP);
    
    // Test buttons for 5 seconds
    unsigned long buttonTestStart = millis();
    while (millis() - buttonTestStart < 5000) {
        if (digitalRead(WIO_KEY_A) == LOW) {
            Serial.println("Button A pressed!");
            tft.fillRect(0, 50, 320, 20, TFT_BLACK);
            tft.setTextColor(TFT_GREEN, TFT_BLACK);
            tft.drawString("Button A pressed!", 0, 50, 2);
            delay(200);
        }
        if (digitalRead(WIO_KEY_B) == LOW) {
            Serial.println("Button B pressed!");
            tft.fillRect(0, 50, 320, 20, TFT_BLACK);
            tft.setTextColor(TFT_YELLOW, TFT_BLACK);
            tft.drawString("Button B pressed!", 0, 50, 2);
            delay(200);
        }
        if (digitalRead(WIO_KEY_C) == LOW) {
            Serial.println("Button C pressed!");
            tft.fillRect(0, 50, 320, 20, TFT_BLACK);
            tft.setTextColor(TFT_RED, TFT_BLACK);
            tft.drawString("Button C pressed!", 0, 50, 2);
            delay(200);
        }
        delay(50);
    }
    
    // Test 5: Success message
    Serial.println("Test 5: Success message");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    tft.drawString("DISPLAY", 50, 50, 6);
    tft.drawString("WORKING!", 50, 100, 6);
    
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.drawString("If you can see this,", 0, 160, 2);
    tft.drawString("display is functional", 0, 180, 2);
    
    Serial.println("=== TEST CYCLE COMPLETE ===");
    Serial.println("If you see colors and text, display is working!");
    Serial.println("Waiting 10 seconds before next cycle...");
    
    delay(10000);
}
