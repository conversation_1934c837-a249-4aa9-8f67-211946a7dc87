/*
 * Wio Terminal Display Fix
 * This forces the correct TFT_eSPI configuration for Wio Terminal
 * Based on the working reference code requirements
 */

// Force Wio Terminal TFT configuration before including TFT_eSPI
#define USER_SETUP_LOADED 1

// Define the display driver
#define ILI9341_DRIVER 1

// Define the display size
#define TFT_WIDTH  240
#define TFT_HEIGHT 320

// Define the pins for Wio Terminal
#define TFT_MISO 19
#define TFT_MOSI 23  
#define TFT_SCLK 18
#define TFT_CS   15
#define TFT_DC    2
#define TFT_RST   4

// Load fonts (as mentioned in reference)
#define LOAD_GLCD   1
#define LOAD_FONT2  1
#define LOAD_FONT4  1
#define LOAD_FONT6  1
#define LOAD_FONT7  1
#define LOAD_FONT8  1
#define LOAD_GFXFF  1

// SPI frequency
#define SPI_FREQUENCY  27000000

#include <TFT_eSPI.h>
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup() {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== WIO TERMINAL DISPLAY FIX ===");
    Serial.println("Using forced TFT_eSPI configuration...");
    
    // Initialize exactly like the working reference
    Serial.println("Initializing display...");
    tft.init();
    tft.setRotation(1);
    Serial.println("Display initialized with rotation 1");
    
    // Test basic functionality like the reference
    Serial.println("Testing display with reference pattern...");
    
    // Test 1: Basic colors
    Serial.println("Test 1: Red screen");
    tft.fillScreen(TFT_RED);
    delay(2000);
    
    Serial.println("Test 2: Green screen");
    tft.fillScreen(TFT_GREEN);
    delay(2000);
    
    Serial.println("Test 3: Blue screen");
    tft.fillScreen(TFT_BLUE);
    delay(2000);
    
    Serial.println("Test 4: Black background");
    tft.fillScreen(TFT_BLACK);
    delay(1000);
    
    // Test 2: Text like reference
    Serial.println("Test 5: Text rendering");
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    
    // Use the exact same text calls as the working reference
    tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
    tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 16, 2);
    tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 32, 2);
    tft.drawString("abcdefghijklmnopqrstuvw", 0, 48, 2);
    
    Serial.println("Should see green text on black background");
    delay(3000);
    
    // Test 3: Large numbers like reference
    Serial.println("Test 6: Large numbers");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_YELLOW, TFT_BLACK);
    
    tft.drawString("0123", 0, 0, 8);
    tft.drawString("4567", 0, 72, 8);
    
    Serial.println("Should see large yellow numbers");
    delay(3000);
    
    // Test 4: Success message
    Serial.println("Test 7: Success message");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.drawString("WIO TERMINAL", 0, 50, 4);
    tft.drawString("DISPLAY WORKING!", 0, 100, 4);
    
    Serial.println("=== DISPLAY TEST COMPLETE ===");
    Serial.println("If you can see text and colors, display is now working!");
}

void loop() {
    // Continuous blink to show program is running
    static unsigned long lastBlink = 0;
    static bool ledState = false;
    static int testCycle = 0;
    
    if (millis() - lastBlink > 2000) {
        ledState = !ledState;
        digitalWrite(LED_BUILTIN, ledState);
        testCycle++;
        
        Serial.print("Cycle #");
        Serial.print(testCycle);
        Serial.println(ledState ? " - LED ON" : " - LED OFF");
        
        // Flash the screen to show it's working
        if (testCycle % 5 == 0) {
            Serial.println("Screen flash test...");
            tft.fillScreen(TFT_WHITE);
            delay(200);
            tft.fillScreen(TFT_BLACK);
            tft.setTextColor(TFT_GREEN, TFT_BLACK);
            tft.drawString("DISPLAY OK", 50, 100, 4);
        }
        
        lastBlink = millis();
    }
}
