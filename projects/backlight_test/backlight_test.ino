/*
 * Wio Terminal Backlight and Display Test
 * This test focuses on backlight control and alternative display methods
 */

#include <TFT_eSPI.h>
#include <SPI.h>

TFT_eSPI tft = TFT_eSPI();

// Wio Terminal specific pins
#define LCD_BACKLIGHT (72Ul) // Control Pin of LCD

void setup() {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== WIO TERMINAL BACKLIGHT TEST ===");
    
    // Initialize backlight control
    Serial.println("Setting up backlight control...");
    pinMode(LCD_BACKLIGHT, OUTPUT);
    
    // Turn on backlight explicitly
    Serial.println("Turning ON backlight...");
    digitalWrite(LCD_BACKLIGHT, HIGH);
    delay(1000);
    
    Serial.println("Backlight should be ON now");
    
    // Initialize display
    Serial.println("Initializing display...");
    tft.init();
    tft.setRotation(1);
    Serial.println("Display initialized");
    
    // Test with maximum brightness colors
    Serial.println("Testing with bright white...");
    tft.fillScreen(TFT_WHITE);
    delay(3000);
    
    Serial.println("Testing with bright red...");
    tft.fillScreen(TFT_RED);
    delay(3000);
    
    Serial.println("Testing with bright green...");
    tft.fillScreen(TFT_GREEN);
    delay(3000);
    
    Serial.println("Testing with bright blue...");
    tft.fillScreen(TFT_BLUE);
    delay(3000);
    
    // Test backlight control
    Serial.println("Testing backlight OFF...");
    digitalWrite(LCD_BACKLIGHT, LOW);
    tft.fillScreen(TFT_WHITE);
    delay(3000);
    
    Serial.println("Testing backlight ON again...");
    digitalWrite(LCD_BACKLIGHT, HIGH);
    delay(1000);
    
    // Final test with text
    Serial.println("Final test with large text...");
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(3);
    tft.setCursor(10, 50);
    tft.print("WIO TERMINAL");
    tft.setCursor(10, 100);
    tft.print("DISPLAY TEST");
    
    Serial.println("=== BACKLIGHT TEST COMPLETE ===");
    Serial.println("If screen is still black, there may be a hardware issue");
}

void loop() {
    static unsigned long lastTest = 0;
    static bool backlightState = true;
    static int cycle = 0;
    
    if (millis() - lastTest > 5000) {
        cycle++;
        Serial.print("Cycle #");
        Serial.println(cycle);
        
        // Toggle backlight
        backlightState = !backlightState;
        digitalWrite(LCD_BACKLIGHT, backlightState);
        Serial.print("Backlight: ");
        Serial.println(backlightState ? "ON" : "OFF");
        
        // Flash screen
        if (backlightState) {
            tft.fillScreen(TFT_YELLOW);
            delay(500);
            tft.fillScreen(TFT_MAGENTA);
            delay(500);
            tft.fillScreen(TFT_CYAN);
        }
        
        // LED indicator
        digitalWrite(LED_BUILTIN, backlightState);
        
        lastTest = millis();
    }
}
