#!/bin/bash

# Arduino CLI Setup Script for Wio Terminal Development
# This script sets up the Arduino CLI environment for compiling Wio Terminal projects

echo "Setting up Arduino CLI for Wio Terminal development..."

# Check if arduino-cli is installed
if ! command -v arduino-cli &> /dev/null; then
    echo "Arduino CLI not found. Please install it first:"
    echo "1. Download from: https://github.com/arduino/arduino-cli/releases"
    echo "2. Or use package manager: brew install arduino-cli"
    echo "3. Or run: curl -fsSL https://raw.githubusercontent.com/arduino/arduino-cli/master/install.sh | sh"
    exit 1
fi

echo "Arduino CLI found. Setting up configuration..."

# Initialize configuration
arduino-cli config init

# Update package index
echo "Updating package index..."
arduino-cli core update-index

# Install Seeed SAMD boards
echo "Installing Seeed SAMD boards..."
arduino-cli config add board_manager.additional_urls https://files.seeedstudio.com/arduino/package_seeedstudio_boards_index.json
arduino-cli core update-index
arduino-cli core install Seeed:samd

# Install required libraries
echo "Installing required libraries..."
arduino-cli lib install "TFT_eSPI"
arduino-cli lib install "ArduinoJson"

# List available boards to verify installation
echo "Available Seeed boards:"
arduino-cli board listall | grep -i seeed

echo "Setup complete!"
echo ""
echo "To compile the countdown timer project:"
echo "arduino-cli compile --fqbn Seeed:samd:seeed_wio_terminal projects/countdown_timer.ino"
echo ""
echo "To upload to Wio Terminal:"
echo "arduino-cli upload -p /dev/ttyACM0 --fqbn Seeed:samd:seeed_wio_terminal projects/countdown_timer.ino"
echo "(Replace /dev/ttyACM0 with your actual port)"
