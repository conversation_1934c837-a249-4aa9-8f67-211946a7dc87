/*
 * Raw SPI Display Test for Wio Terminal
 * This bypasses TFT_eSPI and talks directly to the display controller
 */

#include <SPI.h>

// Wio Terminal display pins
#define TFT_CS   15
#define TFT_DC   2
#define TFT_RST  4
#define LCD_BACKLIGHT 72

// ILI9341 commands
#define ILI9341_SWRESET    0x01
#define ILI9341_SLPOUT     0x11
#define ILI9341_DISPON     0x29
#define ILI9341_CASET      0x2A
#define ILI9341_PASET      0x2B
#define ILI9341_RAMWR      0x2C
#define ILI9341_MADCTL     0x36
#define ILI9341_COLMOD     0x3A

void setup() {
    Serial.begin(115200);
    delay(3000);
    Serial.println("=== RAW SPI DISPLAY TEST ===");
    
    // Setup pins
    pinMode(TFT_CS, OUTPUT);
    pinMode(TFT_DC, OUTPUT);
    pinMode(TFT_RST, OUTPUT);
    pinMode(LCD_BACKLIGHT, OUTPUT);
    
    // Turn on backlight
    digitalWrite(LCD_BACKLIGHT, HIGH);
    Serial.println("Backlight ON");
    
    // Initialize SPI
    SPI.begin();
    SPI.setClockDivider(SPI_CLOCK_DIV2);
    SPI.setDataMode(SPI_MODE0);
    Serial.println("SPI initialized");
    
    // Reset display
    Serial.println("Resetting display...");
    digitalWrite(TFT_RST, LOW);
    delay(100);
    digitalWrite(TFT_RST, HIGH);
    delay(100);
    
    // Initialize display controller
    Serial.println("Initializing ILI9341...");
    
    // Software reset
    writeCommand(ILI9341_SWRESET);
    delay(150);
    
    // Sleep out
    writeCommand(ILI9341_SLPOUT);
    delay(150);
    
    // Color mode - 16 bit
    writeCommand(ILI9341_COLMOD);
    writeData(0x55);
    
    // Memory access control (rotation)
    writeCommand(ILI9341_MADCTL);
    writeData(0x48); // Landscape mode
    
    // Display on
    writeCommand(ILI9341_DISPON);
    delay(100);
    
    Serial.println("Display initialized");
    
    // Test 1: Fill screen with red
    Serial.println("Test 1: Red screen");
    fillScreen(0xF800); // Red in RGB565
    delay(2000);
    
    // Test 2: Fill screen with green
    Serial.println("Test 2: Green screen");
    fillScreen(0x07E0); // Green in RGB565
    delay(2000);
    
    // Test 3: Fill screen with blue
    Serial.println("Test 3: Blue screen");
    fillScreen(0x001F); // Blue in RGB565
    delay(2000);
    
    // Test 4: Fill screen with white
    Serial.println("Test 4: White screen");
    fillScreen(0xFFFF); // White in RGB565
    delay(2000);
    
    // Test 5: Fill screen with black
    Serial.println("Test 5: Black screen");
    fillScreen(0x0000); // Black in RGB565
    delay(2000);
    
    Serial.println("=== RAW SPI TEST COMPLETE ===");
    Serial.println("If you saw colors, the display controller is working!");
}

void loop() {
    static unsigned long lastTest = 0;
    static int colorIndex = 0;
    
    if (millis() - lastTest > 2000) {
        uint16_t colors[] = {0xF800, 0x07E0, 0x001F, 0xFFFF, 0xFFE0, 0xF81F, 0x0000};
        String colorNames[] = {"RED", "GREEN", "BLUE", "WHITE", "YELLOW", "MAGENTA", "BLACK"};
        
        Serial.print("Color test: ");
        Serial.println(colorNames[colorIndex]);
        
        fillScreen(colors[colorIndex]);
        
        colorIndex = (colorIndex + 1) % 7;
        lastTest = millis();
        
        // LED indicator
        digitalWrite(LED_BUILTIN, colorIndex % 2);
    }
}

void writeCommand(uint8_t cmd) {
    digitalWrite(TFT_CS, LOW);
    digitalWrite(TFT_DC, LOW);  // Command mode
    SPI.transfer(cmd);
    digitalWrite(TFT_CS, HIGH);
}

void writeData(uint8_t data) {
    digitalWrite(TFT_CS, LOW);
    digitalWrite(TFT_DC, HIGH); // Data mode
    SPI.transfer(data);
    digitalWrite(TFT_CS, HIGH);
}

void writeData16(uint16_t data) {
    digitalWrite(TFT_CS, LOW);
    digitalWrite(TFT_DC, HIGH); // Data mode
    SPI.transfer(data >> 8);    // High byte
    SPI.transfer(data & 0xFF);  // Low byte
    digitalWrite(TFT_CS, HIGH);
}

void setWindow(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1) {
    // Column address set
    writeCommand(ILI9341_CASET);
    writeData16(x0);
    writeData16(x1);
    
    // Page address set
    writeCommand(ILI9341_PASET);
    writeData16(y0);
    writeData16(y1);
    
    // Memory write
    writeCommand(ILI9341_RAMWR);
}

void fillScreen(uint16_t color) {
    setWindow(0, 0, 319, 239); // 320x240 in landscape
    
    digitalWrite(TFT_CS, LOW);
    digitalWrite(TFT_DC, HIGH); // Data mode
    
    for (uint32_t i = 0; i < 76800; i++) { // 320 * 240 pixels
        SPI.transfer(color >> 8);    // High byte
        SPI.transfer(color & 0xFF);  // Low byte
    }
    
    digitalWrite(TFT_CS, HIGH);
}
